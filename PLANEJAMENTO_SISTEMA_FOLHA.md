# Sistema de Folha de Pagamento - Painel ABZ
## Planejamento e Arquitetura

### 1. ANÁLISE DOS DADOS EXISTENTES

#### PDF Analisado: "Extrato Mensal - 06.2025 ABZ FMS.pdf"
**Estrutura identificada:**
- **Empresa:** AGUAS BRASILEIRAS SERVICOS E CONSULTORIAS EM ATIVIDADES MARITIMAS LTDA
- **CNPJ:** 17.784.306/0001-89
- **Departamento:** 33 - ABZ - FMS - FIRST MARINE SOLUTIONS
- **Período:** 06/2025 (01/06/2025 a 30/06/2025)

**Funcionários identificados:**
- BIANCA FILIPPI (579) - Processador de Dados - Salário: R$ 5.466,67
- DANIEL ARAGAO MAGALHAES (565) - Processador de Dados - Salário: R$ 5.466,67

**Códigos de Proventos (P):**
- 1: <PERSON><PERSON>
- 63: Adicional de Sobreaviso 20%
- 125: Folga Indenizada
- 127: Reflexo DSR s/adicional noturno
- 131: Adicional Noturno 20%
- 138: Dobra
- 213: Adicional de Periculosidade 30%

**Códigos de Descontos (D):**
- 104: INSS
- 108: IRRF

**Outros (O):**
- 119: FGTS 8%

### 2. LEGISLAÇÃO TRABALHISTA BRASILEIRA 2025

#### INSS 2025:
- **Salário mínimo:** R$ 1.518,00
- **Teto previdenciário:** R$ 8.157,41
- **Desconto máximo:** R$ 951,62

**Alíquotas progressivas:**
1. Até R$ 1.518,00: 7,5%
2. De R$ 1.518,01 até R$ 2.793,88: 9% (parcela a deduzir: R$ 22,77)
3. De R$ 2.793,89 até R$ 4.190,83: 12% (parcela a deduzir: R$ 106,59)
4. De R$ 4.190,84 até R$ 8.157,41: 14% (parcela a deduzir: R$ 190,40)

#### IRRF 2025 (vigente a partir de maio/2025):
- **Faixa de isenção:** até R$ 3.036,00 (rendimento bruto) ou R$ 2.428,80 (base de cálculo)
- **Dedução simplificada:** R$ 607,20
- **Dedução por dependente:** R$ 189,59

**Alíquotas progressivas:**
1. Até R$ 2.428,80: 0% (isento)
2. De R$ 2.428,81 até R$ 2.826,65: 7,5% (parcela a deduzir: R$ 182,16)
3. De R$ 2.826,66 até R$ 3.751,05: 15% (parcela a deduzir: R$ 394,16)
4. De R$ 3.751,06 até R$ 4.664,68: 22,5% (parcela a deduzir: R$ 675,49)
5. Acima de R$ 4.664,68: 27,5% (parcela a deduzir: R$ 908,73)

#### FGTS: 8% sobre o salário bruto

### 3. ARQUITETURA DO SISTEMA

#### 3.1 Estrutura de Banco de Dados (Supabase)

**Tabelas principais:**

```sql
-- Empresas/Departamentos
CREATE TABLE payroll_companies (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR NOT NULL,
  cnpj VARCHAR(18) UNIQUE NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE payroll_departments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_id UUID REFERENCES payroll_companies(id),
  code VARCHAR NOT NULL,
  name VARCHAR NOT NULL,
  description TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Funcionários (integração com sistema existente)
CREATE TABLE payroll_employees (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  employee_id UUID REFERENCES employees(id), -- Referência ao sistema existente
  company_id UUID REFERENCES payroll_companies(id),
  department_id UUID REFERENCES payroll_departments(id),
  registration_number VARCHAR,
  position VARCHAR,
  base_salary DECIMAL(10,2),
  admission_date DATE,
  status VARCHAR DEFAULT 'active',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Perfis de Cálculo
CREATE TABLE payroll_calculation_profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR NOT NULL,
  description TEXT,
  rules JSONB, -- Regras específicas do perfil
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Códigos de Proventos/Descontos
CREATE TABLE payroll_codes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  code VARCHAR NOT NULL,
  type VARCHAR CHECK (type IN ('provento', 'desconto', 'outros')),
  name VARCHAR NOT NULL,
  description TEXT,
  calculation_type VARCHAR, -- 'percentage', 'fixed', 'formula'
  value DECIMAL(10,4),
  formula TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Folhas de Pagamento
CREATE TABLE payroll_sheets (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_id UUID REFERENCES payroll_companies(id),
  department_id UUID REFERENCES payroll_departments(id),
  reference_month INTEGER,
  reference_year INTEGER,
  period_start DATE,
  period_end DATE,
  status VARCHAR DEFAULT 'draft', -- draft, calculated, approved, paid
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Itens da Folha de Pagamento
CREATE TABLE payroll_sheet_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  sheet_id UUID REFERENCES payroll_sheets(id),
  employee_id UUID REFERENCES payroll_employees(id),
  code_id UUID REFERENCES payroll_codes(id),
  quantity DECIMAL(10,2) DEFAULT 1,
  value DECIMAL(10,2),
  calculated_value DECIMAL(10,2),
  created_at TIMESTAMP DEFAULT NOW()
);

-- Resumos por Funcionário
CREATE TABLE payroll_employee_summaries (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  sheet_id UUID REFERENCES payroll_sheets(id),
  employee_id UUID REFERENCES payroll_employees(id),
  gross_salary DECIMAL(10,2),
  total_earnings DECIMAL(10,2),
  total_deductions DECIMAL(10,2),
  inss_base DECIMAL(10,2),
  inss_value DECIMAL(10,2),
  irrf_base DECIMAL(10,2),
  irrf_value DECIMAL(10,2),
  fgts_base DECIMAL(10,2),
  fgts_value DECIMAL(10,2),
  net_salary DECIMAL(10,2),
  created_at TIMESTAMP DEFAULT NOW()
);
```

#### 3.2 Estrutura de Componentes (Next.js)

```
src/
├── app/
│   ├── folha-pagamento/
│   │   ├── page.tsx                    # Dashboard principal
│   │   ├── empresas/
│   │   │   ├── page.tsx               # Lista de empresas
│   │   │   └── [id]/page.tsx          # Detalhes da empresa
│   │   ├── funcionarios/
│   │   │   ├── page.tsx               # Lista de funcionários
│   │   │   └── [id]/page.tsx          # Detalhes do funcionário
│   │   ├── perfis-calculo/
│   │   │   ├── page.tsx               # Perfis de cálculo
│   │   │   └── [id]/page.tsx          # Editor de perfil
│   │   ├── folhas/
│   │   │   ├── page.tsx               # Lista de folhas
│   │   │   ├── nova/page.tsx          # Nova folha
│   │   │   └── [id]/
│   │   │       ├── page.tsx           # Visualizar folha
│   │   │       ├── editar/page.tsx    # Editar folha
│   │   │       └── relatorios/page.tsx # Relatórios
│   │   └── configuracoes/
│   │       ├── page.tsx               # Configurações gerais
│   │       └── codigos/page.tsx       # Códigos de provento/desconto
├── components/
│   ├── folha-pagamento/
│   │   ├── PayrollCard.tsx            # Card principal no dashboard
│   │   ├── PayrollDashboard.tsx       # Dashboard do módulo
│   │   ├── EmployeeList.tsx           # Lista de funcionários
│   │   ├── PayrollCalculator.tsx      # Calculadora de folha
│   │   ├── PayrollSheet.tsx           # Visualizador de folha
│   │   ├── ReportGenerator.tsx        # Gerador de relatórios
│   │   └── InvoiceGenerator.tsx       # Gerador de invoices
├── lib/
│   ├── payroll/
│   │   ├── calculations.ts            # Lógica de cálculos
│   │   ├── legal-tables.ts            # Tabelas da legislação
│   │   ├── pdf-generator.ts           # Geração de PDFs
│   │   └── excel-generator.ts         # Geração de planilhas
└── types/
    └── payroll.ts                     # Tipos TypeScript
```

#### 3.3 Integração com Sistema Existente

**Compartilhamento de funcionários:**
- Usar tabela `employees` existente como referência
- Criar tabela `payroll_employees` para dados específicos da folha
- Sincronização automática de dados básicos

**Design System:**
- Usar componentes existentes do Painel ABZ
- Manter paleta de cores e tipografia
- Seguir padrões de layout estabelecidos

**Autenticação:**
- Usar sistema JWT existente
- Aproveitar middleware de autenticação
- Manter controle de permissões por perfil

### 4. FUNCIONALIDADES PRINCIPAIS

#### 4.1 Gestão de Funcionários
- Cadastro integrado com sistema existente
- Perfis de cálculo personalizáveis
- Histórico de alterações salariais

#### 4.2 Cálculo de Folha
- Motor de cálculo baseado na legislação brasileira
- Cálculos automáticos de INSS, IRRF e FGTS
- Suporte a adicionais e descontos personalizados
- Validação de regras trabalhistas

#### 4.3 Relatórios e Exportação
- Geração de PDFs no formato padrão
- Exportação para Excel/CSV
- Relatórios gerenciais
- Guias de recolhimento (GPS, DARF)

#### 4.4 Sistema de Invoices
- Geração automática de faturas para clientes
- Templates personalizáveis
- Controle de pagamentos
- Integração com sistema financeiro

### 5. PRÓXIMOS PASSOS

1. **Desenvolvimento do Backend**
   - Criar APIs para CRUD das entidades
   - Implementar motor de cálculo
   - Configurar validações

2. **Desenvolvimento do Frontend**
   - Criar componentes base
   - Implementar interfaces de usuário
   - Integrar com APIs

3. **Sistema de Relatórios**
   - Implementar geração de PDFs
   - Criar templates de planilhas
   - Desenvolver sistema de invoices

4. **Testes e Validação**
   - Testes unitários dos cálculos
   - Validação com dados reais
   - Testes de integração

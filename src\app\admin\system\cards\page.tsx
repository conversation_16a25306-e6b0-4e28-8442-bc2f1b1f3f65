'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import { 
  FiPlus, 
  FiEdit, 
  FiTrash2, 
  FiRefreshCw, 
  FiDatabase,
  FiActivity,
  FiBookOpen,
  FiClipboard,
  FiFileText,
  FiBriefcase,
  FiCalendar,
  FiRss,
  FiDollarSign,
  FiSettings,
  FiUsers
} from 'react-icons/fi';

interface DashboardCard {
  id: string;
  title: string;
  description: string;
  href: string;
  icon_name: string;
  color: string;
  hover_color: string;
  external: boolean;
  enabled: boolean;
  order: number;
  admin_only: boolean;
  manager_only: boolean;
  allowed_roles: string[];
  allowed_user_ids: string[];
  created_at?: string;
  updated_at?: string;
}

const iconOptions = [
  { value: 'FiActivity', label: 'Atividade', icon: FiActivity },
  { value: 'FiBookOpen', label: 'Livro', icon: FiBookOpen },
  { value: 'FiClipboard', label: 'Clipboard', icon: FiClipboard },
  { value: 'FiFileText', label: 'Arquivo', icon: FiFileText },
  { value: 'FiBriefcase', label: 'Maleta', icon: FiBriefcase },
  { value: 'FiCalendar', label: 'Calendário', icon: FiCalendar },
  { value: 'FiRss', label: 'RSS', icon: FiRss },
  { value: 'FiDollarSign', label: 'Dólar', icon: FiDollarSign },
  { value: 'FiSettings', label: 'Configurações', icon: FiSettings },
  { value: 'FiUsers', label: 'Usuários', icon: FiUsers },
];

const colorOptions = [
  { value: 'bg-abz-blue', label: 'Azul ABZ' },
  { value: 'bg-green-500', label: 'Verde' },
  { value: 'bg-blue-500', label: 'Azul' },
  { value: 'bg-purple-500', label: 'Roxo' },
  { value: 'bg-red-500', label: 'Vermelho' },
  { value: 'bg-yellow-500', label: 'Amarelo' },
  { value: 'bg-indigo-500', label: 'Índigo' },
  { value: 'bg-pink-500', label: 'Rosa' },
];

export default function AdminCardsPage() {
  const router = useRouter();
  const [cards, setCards] = useState<DashboardCard[]>([]);
  const [loading, setLoading] = useState(true);
  const [migrationStatus, setMigrationStatus] = useState<any>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingCard, setEditingCard] = useState<DashboardCard | null>(null);

  // Verificar autenticação e permissões
  useEffect(() => {
    const token = localStorage.getItem('token');
    const userStr = localStorage.getItem('user');
    
    if (!token || !userStr) {
      router.push('/login');
      return;
    }

    const user = JSON.parse(userStr);
    if (user.role !== 'ADMIN') {
      toast.error('Acesso negado. Apenas administradores podem acessar esta página.');
      router.push('/dashboard');
      return;
    }

    loadCards();
    checkMigrationStatus();
  }, [router]);

  const loadCards = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/cards', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setCards(data);
      } else {
        toast.error('Erro ao carregar cards');
      }
    } catch (error) {
      console.error('Erro ao carregar cards:', error);
      toast.error('Erro ao carregar cards');
    } finally {
      setLoading(false);
    }
  };

  const checkMigrationStatus = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/admin/migrate-cards', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const status = await response.json();
        setMigrationStatus(status);
      }
    } catch (error) {
      console.error('Erro ao verificar status de migração:', error);
    }
  };

  const handleMigration = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/admin/migrate-cards', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const result = await response.json();
        toast.success(`Migração concluída: ${result.success} sucessos, ${result.errors} erros`);
        loadCards();
        checkMigrationStatus();
      } else {
        toast.error('Erro na migração');
      }
    } catch (error) {
      console.error('Erro na migração:', error);
      toast.error('Erro na migração');
    }
  };

  const toggleCardEnabled = async (cardId: string, enabled: boolean) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/admin/cards/${cardId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ enabled })
      });

      if (response.ok) {
        toast.success(`Card ${enabled ? 'ativado' : 'desativado'} com sucesso`);
        loadCards();
      } else {
        toast.error('Erro ao atualizar card');
      }
    } catch (error) {
      console.error('Erro ao atualizar card:', error);
      toast.error('Erro ao atualizar card');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <FiRefreshCw className="animate-spin text-2xl" />
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Gerenciamento de Cards</h1>
          <p className="text-muted-foreground">
            Gerencie os cards do dashboard do sistema
          </p>
        </div>
        
        <div className="flex gap-2">
          <Button onClick={handleMigration} variant="outline">
            <FiDatabase className="mr-2 h-4 w-4" />
            Migrar Dados
          </Button>
          
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <FiPlus className="mr-2 h-4 w-4" />
                Novo Card
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>
                  {editingCard ? 'Editar Card' : 'Novo Card'}
                </DialogTitle>
                <DialogDescription>
                  {editingCard 
                    ? 'Edite as informações do card' 
                    : 'Crie um novo card para o dashboard'
                  }
                </DialogDescription>
              </DialogHeader>
              {/* Formulário será implementado na próxima parte */}
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Status da Migração */}
      {migrationStatus && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FiDatabase className="h-5 w-5" />
              Status da Migração
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-3 gap-4">
              <div>
                <p className="text-sm text-muted-foreground">Cards no Supabase</p>
                <p className="text-2xl font-bold">{migrationStatus.supabaseCount}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Cards Hardcoded</p>
                <p className="text-2xl font-bold">{migrationStatus.hardcodedCount}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Status</p>
                <Badge variant={migrationStatus.status === 'synced' ? 'default' : 'destructive'}>
                  {migrationStatus.status === 'synced' ? 'Sincronizado' : 'Precisa Migrar'}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Tabela de Cards */}
      <Card>
        <CardHeader>
          <CardTitle>Cards do Dashboard</CardTitle>
          <CardDescription>
            {cards.length} cards encontrados
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Título</TableHead>
                <TableHead>Descrição</TableHead>
                <TableHead>Ícone</TableHead>
                <TableHead>Permissões</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Ações</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {cards.map((card) => {
                const IconComponent = iconOptions.find(opt => opt.value === card.icon_name)?.icon || FiActivity;
                
                return (
                  <TableRow key={card.id}>
                    <TableCell className="font-medium">{card.title}</TableCell>
                    <TableCell className="max-w-xs truncate">{card.description}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <IconComponent className="h-4 w-4" />
                        <span className="text-sm text-muted-foreground">{card.icon_name}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-1">
                        {card.admin_only && <Badge variant="destructive" className="text-xs">Admin</Badge>}
                        {card.manager_only && <Badge variant="secondary" className="text-xs">Manager</Badge>}
                        {!card.admin_only && !card.manager_only && <Badge variant="outline" className="text-xs">Todos</Badge>}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Switch
                        checked={card.enabled}
                        onCheckedChange={(enabled) => toggleCardEnabled(card.id, enabled)}
                      />
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setEditingCard(card);
                            setIsDialogOpen(true);
                          }}
                        >
                          <FiEdit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-red-600 hover:text-red-700"
                        >
                          <FiTrash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}

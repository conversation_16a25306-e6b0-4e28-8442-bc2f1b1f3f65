#!/usr/bin/env node

/**
 * Script para configurar automaticamente o banco de dados do módulo de folha de pagamento
 * Sistema de Folha de Pagamento - Painel ABZ
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// Configuração do Supabase
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Erro: Variáveis de ambiente do Supabase não encontradas!');
  console.error('Verifique se NEXT_PUBLIC_SUPABASE_URL e SUPABASE_SERVICE_KEY estão definidas em .env.local');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// SQL para criar as tabelas
const createTablesSQL = `
-- Script para criar as tabelas do módulo de folha de pagamento
-- Sistema de Folha de Pagamento - Painel ABZ

-- Habilitar extensões necessárias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Tabela de empresas/clientes para folha de pagamento
CREATE TABLE IF NOT EXISTS payroll_companies (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(255) NOT NULL,
  cnpj VARCHAR(18) UNIQUE NOT NULL,
  address TEXT,
  phone VARCHAR(20),
  email VARCHAR(255),
  contact_person VARCHAR(255),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de departamentos
CREATE TABLE IF NOT EXISTS payroll_departments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  company_id UUID REFERENCES payroll_companies(id) ON DELETE CASCADE,
  code VARCHAR(10) NOT NULL,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(company_id, code)
);

-- Tabela de cargos
CREATE TABLE IF NOT EXISTS payroll_positions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  company_id UUID REFERENCES payroll_companies(id) ON DELETE CASCADE,
  code VARCHAR(20) NOT NULL,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  base_salary DECIMAL(10,2) DEFAULT 0,
  calculation_profile_id UUID,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(company_id, code)
);

-- Tabela de perfis de cálculo
CREATE TABLE IF NOT EXISTS payroll_calculation_profiles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  company_id UUID REFERENCES payroll_companies(id),
  rules JSONB DEFAULT '{}',
  is_default BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Adicionar FK para perfis de cálculo nos cargos
ALTER TABLE payroll_positions 
ADD CONSTRAINT fk_positions_calculation_profile 
FOREIGN KEY (calculation_profile_id) REFERENCES payroll_calculation_profiles(id);

-- Tabela de funcionários da folha (integração com sistema existente)
CREATE TABLE IF NOT EXISTS payroll_employees (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  employee_id UUID,
  company_id UUID REFERENCES payroll_companies(id) ON DELETE CASCADE,
  department_id UUID REFERENCES payroll_departments(id),
  position_id UUID REFERENCES payroll_positions(id),
  calculation_profile_id UUID REFERENCES payroll_calculation_profiles(id),
  registration_number VARCHAR(20),
  name VARCHAR(255) NOT NULL,
  cpf VARCHAR(14),
  position_name VARCHAR(255),
  base_salary DECIMAL(10,2) NOT NULL DEFAULT 0,
  admission_date DATE,
  termination_date DATE,
  status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'terminated')),
  bank_code VARCHAR(10),
  bank_agency VARCHAR(20),
  bank_account VARCHAR(30),
  pis_pasep VARCHAR(20),
  dependents INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(company_id, registration_number)
);

-- Tabela de códigos de proventos/descontos/outros
CREATE TABLE IF NOT EXISTS payroll_codes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  code VARCHAR(10) NOT NULL,
  type VARCHAR(20) NOT NULL CHECK (type IN ('provento', 'desconto', 'outros')),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  calculation_type VARCHAR(20) DEFAULT 'fixed' CHECK (calculation_type IN ('fixed', 'percentage', 'formula', 'legal')),
  value DECIMAL(10,4) DEFAULT 0,
  formula TEXT,
  legal_type VARCHAR(20),
  is_system BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(code, type)
);

-- Tabela de folhas de pagamento
CREATE TABLE IF NOT EXISTS payroll_sheets (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  company_id UUID REFERENCES payroll_companies(id) ON DELETE CASCADE,
  department_id UUID REFERENCES payroll_departments(id),
  reference_month INTEGER NOT NULL CHECK (reference_month BETWEEN 1 AND 12),
  reference_year INTEGER NOT NULL CHECK (reference_year >= 2024),
  period_start DATE NOT NULL,
  period_end DATE NOT NULL,
  status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'calculated', 'approved', 'paid', 'cancelled')),
  total_employees INTEGER DEFAULT 0,
  total_gross DECIMAL(12,2) DEFAULT 0,
  total_deductions DECIMAL(12,2) DEFAULT 0,
  total_net DECIMAL(12,2) DEFAULT 0,
  total_inss DECIMAL(12,2) DEFAULT 0,
  total_irrf DECIMAL(12,2) DEFAULT 0,
  total_fgts DECIMAL(12,2) DEFAULT 0,
  notes TEXT,
  created_by UUID,
  approved_by UUID,
  approved_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(company_id, department_id, reference_month, reference_year)
);

-- Tabela de itens da folha de pagamento
CREATE TABLE IF NOT EXISTS payroll_sheet_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  sheet_id UUID REFERENCES payroll_sheets(id) ON DELETE CASCADE,
  employee_id UUID REFERENCES payroll_employees(id) ON DELETE CASCADE,
  code_id UUID REFERENCES payroll_codes(id),
  quantity DECIMAL(10,2) DEFAULT 1,
  reference_value DECIMAL(10,2) DEFAULT 0,
  calculated_value DECIMAL(10,2) NOT NULL DEFAULT 0,
  observation TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de resumos por funcionário
CREATE TABLE IF NOT EXISTS payroll_employee_summaries (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  sheet_id UUID REFERENCES payroll_sheets(id) ON DELETE CASCADE,
  employee_id UUID REFERENCES payroll_employees(id) ON DELETE CASCADE,
  base_salary DECIMAL(10,2) NOT NULL DEFAULT 0,
  total_earnings DECIMAL(10,2) NOT NULL DEFAULT 0,
  total_deductions DECIMAL(10,2) NOT NULL DEFAULT 0,
  total_others DECIMAL(10,2) NOT NULL DEFAULT 0,
  inss_base DECIMAL(10,2) DEFAULT 0,
  irrf_base DECIMAL(10,2) DEFAULT 0,
  fgts_base DECIMAL(10,2) DEFAULT 0,
  inss_value DECIMAL(10,2) DEFAULT 0,
  irrf_value DECIMAL(10,2) DEFAULT 0,
  fgts_value DECIMAL(10,2) DEFAULT 0,
  gross_salary DECIMAL(10,2) NOT NULL DEFAULT 0,
  net_salary DECIMAL(10,2) NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(sheet_id, employee_id)
);

-- Tabela de histórico de alterações
CREATE TABLE IF NOT EXISTS payroll_audit_log (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  table_name VARCHAR(50) NOT NULL,
  record_id UUID NOT NULL,
  action VARCHAR(20) NOT NULL CHECK (action IN ('INSERT', 'UPDATE', 'DELETE')),
  old_values JSONB,
  new_values JSONB,
  changed_by UUID,
  changed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para melhor performance
CREATE INDEX IF NOT EXISTS idx_payroll_employees_company ON payroll_employees(company_id);
CREATE INDEX IF NOT EXISTS idx_payroll_employees_status ON payroll_employees(status);
CREATE INDEX IF NOT EXISTS idx_payroll_sheets_company_period ON payroll_sheets(company_id, reference_year, reference_month);
CREATE INDEX IF NOT EXISTS idx_payroll_sheet_items_sheet ON payroll_sheet_items(sheet_id);
CREATE INDEX IF NOT EXISTS idx_payroll_sheet_items_employee ON payroll_sheet_items(employee_id);
CREATE INDEX IF NOT EXISTS idx_payroll_summaries_sheet ON payroll_employee_summaries(sheet_id);

-- Triggers para atualizar updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Aplicar triggers nas tabelas
DROP TRIGGER IF EXISTS update_payroll_companies_updated_at ON payroll_companies;
CREATE TRIGGER update_payroll_companies_updated_at BEFORE UPDATE ON payroll_companies FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_payroll_departments_updated_at ON payroll_departments;
CREATE TRIGGER update_payroll_departments_updated_at BEFORE UPDATE ON payroll_departments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_payroll_positions_updated_at ON payroll_positions;
CREATE TRIGGER update_payroll_positions_updated_at BEFORE UPDATE ON payroll_positions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_payroll_employees_updated_at ON payroll_employees;
CREATE TRIGGER update_payroll_employees_updated_at BEFORE UPDATE ON payroll_employees FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_payroll_calculation_profiles_updated_at ON payroll_calculation_profiles;
CREATE TRIGGER update_payroll_calculation_profiles_updated_at BEFORE UPDATE ON payroll_calculation_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_payroll_codes_updated_at ON payroll_codes;
CREATE TRIGGER update_payroll_codes_updated_at BEFORE UPDATE ON payroll_codes FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_payroll_sheets_updated_at ON payroll_sheets;
CREATE TRIGGER update_payroll_sheets_updated_at BEFORE UPDATE ON payroll_sheets FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_payroll_sheet_items_updated_at ON payroll_sheet_items;
CREATE TRIGGER update_payroll_sheet_items_updated_at BEFORE UPDATE ON payroll_sheet_items FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_payroll_employee_summaries_updated_at ON payroll_employee_summaries;
CREATE TRIGGER update_payroll_employee_summaries_updated_at BEFORE UPDATE ON payroll_employee_summaries FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
`;

// SQL para inserir dados iniciais
const seedDataSQL = `
-- Script para popular dados iniciais do módulo de folha de pagamento
-- Sistema de Folha de Pagamento - Painel ABZ

-- Inserir códigos padrão do sistema (INSS, IRRF, FGTS)
INSERT INTO payroll_codes (code, type, name, description, calculation_type, legal_type, is_system, is_active) VALUES
-- Descontos obrigatórios
('104', 'desconto', 'INSS', 'Contribuição Previdenciária', 'legal', 'inss', true, true),
('108', 'desconto', 'IRRF', 'Imposto de Renda Retido na Fonte', 'legal', 'irrf', true, true),

-- Outros (FGTS)
('119', 'outros', 'FGTS 8%', 'Fundo de Garantia do Tempo de Serviço', 'legal', 'fgts', true, true),

-- Proventos comuns
('001', 'provento', 'Dias Normais', 'Salário base por dias trabalhados', 'fixed', null, false, true),
('063', 'provento', 'Adicional de Sobreaviso 20%', 'Adicional por sobreaviso', 'percentage', null, false, true),
('125', 'provento', 'Folga Indenizada', 'Pagamento de folga não gozada', 'fixed', null, false, true),
('127', 'provento', 'Reflexo DSR s/adicional noturno', 'Reflexo do adicional noturno no DSR', 'formula', null, false, true),
('131', 'provento', 'Adicional Noturno 20%', 'Adicional por trabalho noturno', 'percentage', null, false, true),
('138', 'provento', 'Dobra', 'Pagamento em dobro por trabalho em feriado', 'percentage', null, false, true),
('213', 'provento', 'Adicional de Periculosidade 30%', 'Adicional por atividade perigosa', 'percentage', null, false, true)

ON CONFLICT (code, type) DO UPDATE SET
  name = EXCLUDED.name,
  description = EXCLUDED.description,
  calculation_type = EXCLUDED.calculation_type,
  legal_type = EXCLUDED.legal_type,
  is_system = EXCLUDED.is_system,
  is_active = EXCLUDED.is_active,
  updated_at = NOW();

-- Inserir empresa exemplo (ABZ Group)
INSERT INTO payroll_companies (id, name, cnpj, address, phone, email, contact_person, is_active) VALUES
('550e8400-e29b-41d4-a716-446655440000', 'AGUAS BRASILEIRAS SERVICOS E CONSULTORIAS EM ATIVIDADES MARITIMAS LTDA', '17.784.306/0001-89', 'Endereço da empresa', '+55 22 99999-9999', '<EMAIL>', 'Caio Correia', true)
ON CONFLICT (cnpj) DO UPDATE SET
  name = EXCLUDED.name,
  address = EXCLUDED.address,
  phone = EXCLUDED.phone,
  email = EXCLUDED.email,
  contact_person = EXCLUDED.contact_person,
  is_active = EXCLUDED.is_active,
  updated_at = NOW();

-- Inserir departamento exemplo
INSERT INTO payroll_departments (id, company_id, code, name, description, is_active) VALUES
('660e8400-e29b-41d4-a716-446655440000', '550e8400-e29b-41d4-a716-446655440000', '33', 'ABZ - FMS - FIRST MARINE SOLUTIONS', 'Departamento de soluções marítimas', true)
ON CONFLICT (company_id, code) DO UPDATE SET
  name = EXCLUDED.name,
  description = EXCLUDED.description,
  is_active = EXCLUDED.is_active,
  updated_at = NOW();

-- Inserir perfil de cálculo padrão
INSERT INTO payroll_calculation_profiles (id, name, description, company_id, rules, is_default, is_active) VALUES
('880e8400-e29b-41d4-a716-446655440000', 'Perfil Padrão ABZ', 'Perfil de cálculo padrão para funcionários da ABZ Group', '550e8400-e29b-41d4-a716-446655440000', '{"inss": true, "irrf": true, "fgts": true, "vale_transporte": 6}', true, true)
ON CONFLICT DO NOTHING;

-- Inserir cargos exemplo
INSERT INTO payroll_positions (id, company_id, code, name, description, base_salary, calculation_profile_id, is_active) VALUES
('990e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440000', 'PROC_DADOS', 'Processador de Dados', 'Profissional responsável pelo processamento de dados', 5466.67, '880e8400-e29b-41d4-a716-446655440000', true),
('990e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440000', 'DEV_FULL', 'Desenvolvedor Full Stack', 'Desenvolvedor de software full stack', 8000.00, '880e8400-e29b-41d4-a716-446655440000', true)
ON CONFLICT (company_id, code) DO UPDATE SET
  name = EXCLUDED.name,
  description = EXCLUDED.description,
  base_salary = EXCLUDED.base_salary,
  calculation_profile_id = EXCLUDED.calculation_profile_id,
  is_active = EXCLUDED.is_active,
  updated_at = NOW();

-- Inserir funcionários exemplo
INSERT INTO payroll_employees (id, company_id, department_id, position_id, calculation_profile_id, registration_number, name, cpf, position_name, base_salary, admission_date, status, dependents) VALUES
('770e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440000', '660e8400-e29b-41d4-a716-446655440000', '990e8400-e29b-41d4-a716-446655440001', '880e8400-e29b-41d4-a716-446655440000', '579', 'BIANCA FILIPPI', '000.000.000-00', 'Processador de Dados', 5466.67, '2024-01-01', 'active', 0),
('770e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440000', '660e8400-e29b-41d4-a716-446655440000', '990e8400-e29b-41d4-a716-446655440001', '880e8400-e29b-41d4-a716-446655440000', '565', 'DANIEL ARAGAO MAGALHAES', '000.000.000-01', 'Processador de Dados', 5466.67, '2024-01-01', 'active', 0),
('770e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440000', '660e8400-e29b-41d4-a716-446655440000', '990e8400-e29b-41d4-a716-446655440002', '880e8400-e29b-41d4-a716-446655440000', '001', 'CAIO CORREIA', '000.000.000-02', 'Desenvolvedor Full Stack', 8000.00, '2024-01-01', 'active', 0)
ON CONFLICT (company_id, registration_number) DO UPDATE SET
  name = EXCLUDED.name,
  cpf = EXCLUDED.cpf,
  position_name = EXCLUDED.position_name,
  position_id = EXCLUDED.position_id,
  calculation_profile_id = EXCLUDED.calculation_profile_id,
  base_salary = EXCLUDED.base_salary,
  admission_date = EXCLUDED.admission_date,
  status = EXCLUDED.status,
  dependents = EXCLUDED.dependents,
  updated_at = NOW();
`;

async function setupDatabase() {
  console.log('🚀 Iniciando configuração do banco de dados...');
  console.log(`📡 Conectando ao Supabase: ${supabaseUrl}`);

  try {
    // Executar script de criação de tabelas
    console.log('📋 Criando tabelas...');
    const { error: createError } = await supabase.rpc('exec_sql', { sql: createTablesSQL });
    
    if (createError) {
      console.error('❌ Erro ao criar tabelas:', createError);
      // Tentar executar diretamente
      console.log('🔄 Tentando executar SQL diretamente...');
      const { error: directError } = await supabase
        .from('_temp_sql_execution')
        .select('*')
        .limit(1);
      
      // Se não conseguir, vamos executar por partes
      console.log('📝 Executando SQL por partes...');
      const sqlParts = createTablesSQL.split(';').filter(part => part.trim());
      
      for (let i = 0; i < sqlParts.length; i++) {
        const part = sqlParts[i].trim();
        if (part) {
          try {
            console.log(`   Executando parte ${i + 1}/${sqlParts.length}...`);
            await supabase.rpc('exec_sql', { sql: part + ';' });
          } catch (partError) {
            console.log(`   ⚠️  Parte ${i + 1} pode já existir ou ter erro menor:`, part.substring(0, 50) + '...');
          }
        }
      }
    } else {
      console.log('✅ Tabelas criadas com sucesso!');
    }

    // Executar script de dados iniciais
    console.log('📊 Inserindo dados iniciais...');
    const { error: seedError } = await supabase.rpc('exec_sql', { sql: seedDataSQL });
    
    if (seedError) {
      console.log('🔄 Tentando inserir dados por partes...');
      const seedParts = seedDataSQL.split(';').filter(part => part.trim());
      
      for (let i = 0; i < seedParts.length; i++) {
        const part = seedParts[i].trim();
        if (part) {
          try {
            console.log(`   Inserindo dados ${i + 1}/${seedParts.length}...`);
            await supabase.rpc('exec_sql', { sql: part + ';' });
          } catch (partError) {
            console.log(`   ⚠️  Dados ${i + 1} podem já existir:`, part.substring(0, 50) + '...');
          }
        }
      }
    } else {
      console.log('✅ Dados iniciais inseridos com sucesso!');
    }

    // Verificar se as tabelas foram criadas
    console.log('🔍 Verificando tabelas criadas...');
    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .like('table_name', 'payroll_%');

    if (tablesError) {
      console.log('⚠️  Não foi possível verificar as tabelas, mas provavelmente foram criadas.');
    } else {
      console.log('📋 Tabelas encontradas:');
      tables?.forEach(table => {
        console.log(`   ✅ ${table.table_name}`);
      });
    }

    // Verificar dados inseridos
    console.log('🔍 Verificando dados inseridos...');
    const { data: companies, error: companiesError } = await supabase
      .from('payroll_companies')
      .select('name')
      .limit(5);

    if (!companiesError && companies) {
      console.log('🏢 Empresas cadastradas:');
      companies.forEach(company => {
        console.log(`   ✅ ${company.name}`);
      });
    }

    const { data: employees, error: employeesError } = await supabase
      .from('payroll_employees')
      .select('name, position_name')
      .limit(5);

    if (!employeesError && employees) {
      console.log('👥 Funcionários cadastrados:');
      employees.forEach(employee => {
        console.log(`   ✅ ${employee.name} - ${employee.position_name}`);
      });
    }

    console.log('\n🎉 CONFIGURAÇÃO CONCLUÍDA COM SUCESSO!');
    console.log('🚀 O sistema de folha de pagamento está pronto para uso!');
    console.log('🌐 Acesse: http://localhost:3000/folha-pagamento');

  } catch (error) {
    console.error('❌ Erro geral na configuração:', error);
    console.log('\n📝 INSTRUÇÕES MANUAIS:');
    console.log('1. Acesse: https://supabase.com/dashboard');
    console.log('2. Selecione o projeto: arzvingdtnttiejcvucs');
    console.log('3. Vá em SQL Editor');
    console.log('4. Execute os scripts em: scripts/execute-sql-supabase.md');
  }
}

// Executar o setup
setupDatabase();

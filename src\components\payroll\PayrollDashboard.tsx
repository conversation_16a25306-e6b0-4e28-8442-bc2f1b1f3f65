'use client';

import React, { useState, useEffect } from 'react';
import { 
  Calculator, 
  Users, 
  Building2, 
  FileText, 
  TrendingUp, 
  Plus,
  Search,
  Filter,
  Download
} from 'lucide-react';
import { PayrollDashboardStats } from '@/types/payroll';

/**
 * Dashboard principal do módulo de folha de pagamento
 * Mantém o design system do Painel ABZ
 */
export default function PayrollDashboard() {
  const [stats, setStats] = useState<PayrollDashboardStats>({
    totalCompanies: 0,
    totalEmployees: 0,
    totalActiveSheets: 0,
    totalMonthlyPayroll: 0,
    recentSheets: [],
    monthlyTrends: []
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      // TODO: Implementar chamada para API
      // const response = await fetch('/api/payroll/dashboard');
      // const data = await response.json();
      // setStats(data);
      
      // Dados mockados por enquanto
      setStats({
        totalCompanies: 1,
        totalEmployees: 3,
        totalActiveSheets: 1,
        totalMonthlyPayroll: 21866.68,
        recentSheets: [],
        monthlyTrends: []
      });
    } catch (error) {
      console.error('Erro ao carregar dados do dashboard:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-abz-background p-6">
        <div className="max-w-7xl mx-auto">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-300 rounded w-1/4 mb-6"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="bg-white p-6 rounded-lg shadow-md">
                  <div className="h-4 bg-gray-300 rounded w-3/4 mb-4"></div>
                  <div className="h-8 bg-gray-300 rounded w-1/2"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-abz-background">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-abz-blue/10 rounded-lg">
                <Calculator className="h-6 w-6 text-abz-blue" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-abz-text-dark">
                  Folha de Pagamento
                </h1>
                <p className="text-gray-600">
                  Gestão completa de folha de pagamento
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <button className="bg-abz-blue text-white px-4 py-2 rounded-md hover:bg-abz-blue-dark transition-colors flex items-center space-x-2">
                <Plus className="h-4 w-4" />
                <span>Nova Folha</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto p-6">
        {/* Cards de Estatísticas */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Mensal</p>
                <p className="text-2xl font-bold text-abz-blue">
                  {formatCurrency(stats.totalMonthlyPayroll)}
                </p>
              </div>
              <div className="p-3 bg-abz-blue/10 rounded-lg">
                <TrendingUp className="h-6 w-6 text-abz-blue" />
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Funcionários</p>
                <p className="text-2xl font-bold text-abz-green">
                  {stats.totalEmployees}
                </p>
              </div>
              <div className="p-3 bg-abz-green/10 rounded-lg">
                <Users className="h-6 w-6 text-abz-green" />
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Empresas</p>
                <p className="text-2xl font-bold text-abz-purple">
                  {stats.totalCompanies}
                </p>
              </div>
              <div className="p-3 bg-abz-purple/10 rounded-lg">
                <Building2 className="h-6 w-6 text-abz-purple" />
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Folhas Ativas</p>
                <p className="text-2xl font-bold text-abz-orange">
                  {stats.totalActiveSheets}
                </p>
              </div>
              <div className="p-3 bg-abz-orange/10 rounded-lg">
                <FileText className="h-6 w-6 text-abz-orange" />
              </div>
            </div>
          </div>
        </div>

        {/* Ações Rápidas */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
            <h3 className="text-lg font-semibold text-abz-text-dark mb-4">
              Ações Rápidas
            </h3>
            <div className="space-y-3">
              <button className="w-full bg-abz-blue text-white px-4 py-2 rounded-md hover:bg-abz-blue-dark transition-colors flex items-center space-x-2">
                <Plus className="h-4 w-4" />
                <span>Nova Folha de Pagamento</span>
              </button>
              <button className="w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 transition-colors flex items-center space-x-2">
                <Users className="h-4 w-4" />
                <span>Gerenciar Funcionários</span>
              </button>
              <button className="w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 transition-colors flex items-center space-x-2">
                <Building2 className="h-4 w-4" />
                <span>Gerenciar Empresas</span>
              </button>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
            <h3 className="text-lg font-semibold text-abz-text-dark mb-4">
              Relatórios
            </h3>
            <div className="space-y-3">
              <button className="w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 transition-colors flex items-center space-x-2">
                <Download className="h-4 w-4" />
                <span>Relatório Mensal</span>
              </button>
              <button className="w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 transition-colors flex items-center space-x-2">
                <FileText className="h-4 w-4" />
                <span>Guias de Recolhimento</span>
              </button>
              <button className="w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 transition-colors flex items-center space-x-2">
                <TrendingUp className="h-4 w-4" />
                <span>Análise de Custos</span>
              </button>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
            <h3 className="text-lg font-semibold text-abz-text-dark mb-4">
              Configurações
            </h3>
            <div className="space-y-3">
              <button className="w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 transition-colors">
                Códigos de Folha
              </button>
              <button className="w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 transition-colors">
                Perfis de Cálculo
              </button>
              <button className="w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 transition-colors">
                Tabelas Legais
              </button>
            </div>
          </div>
        </div>

        {/* Folhas Recentes */}
        <div className="bg-white rounded-lg shadow-md border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-abz-text-dark">
                Folhas Recentes
              </h3>
              <div className="flex items-center space-x-2">
                <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                  <Search className="h-4 w-4" />
                </button>
                <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                  <Filter className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
          <div className="p-6">
            {stats.recentSheets.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">Nenhuma folha de pagamento encontrada</p>
                <button className="mt-4 bg-abz-blue text-white px-4 py-2 rounded-md hover:bg-abz-blue-dark transition-colors">
                  Criar primeira folha
                </button>
              </div>
            ) : (
              <div className="space-y-4">
                {/* Lista de folhas será implementada aqui */}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

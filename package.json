{"name": "painel-abz", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3000", "build": "rimraf .next && next build", "clean": "node scripts/clean-next-cache.js", "clear-cache": "node scripts/clear-cache.js", "rebuild": "npm run clean && npm run build", "start": "next start", "start:prod": "cross-env NODE_ENV=production node server.js", "start:80": "cross-env NODE_ENV=production PORT=80 node server.js", "deploy": "npm run build && npm run start:prod", "restart": "node scripts/restart-server.js", "fix-and-restart": "npm run db:fix-avaliacoes && npm run restart", "fix-icons": "npm run clear-cache && npm run dev", "lint": "next lint", "db:init": "npm run db:setup", "db:setup": "npm run db:create-sql-functions && npm run db:create-users-unified && npm run db:setup-avaliacao && npm run db:setup-reimbursement", "db:check": "node scripts/check-table-columns.js", "db:create-sql-functions": "node scripts/create-sql-functions.js", "db:create-exec-sql": "node scripts/create-exec-sql-function.js", "db:create-execute-sql": "node scripts/create-execute-sql-function.js", "db:create-users-unified": "node scripts/run-create-users-unified.js", "db:setup-avaliacao": "node scripts/setup-avaliacao-module.js", "db:setup-reimbursement": "node scripts/setup-reimbursement-settings.js", "db:add-access-history": "node scripts/add-access-history-to-all-users.js", "db:fix-avaliacoes": "node scripts/fix-all.js", "db:check-avaliacoes": "node scripts/check-database.js", "db:insert-sample-data": "node scripts/insert-sample-data.js", "db:verify-solution": "node scripts/verify-solution.js", "db:fix-rls": "node scripts/fix-rls-client-only.js && node scripts/fix-storage-rls.js", "db:fix-sql-functions": "node scripts/create-sql-functions.js", "db:fix-rls-direct": "node scripts/fix-rls-policies-direct.js", "db:fix-rls-client": "node scripts/fix-rls-client.js", "db:fix-rls-client-only": "node scripts/fix-rls-client-only.js", "db:fix-storage-rls": "node scripts/fix-storage-rls.js", "db:make-bucket-public": "node scripts/make-bucket-public.js", "admin:setup": "node scripts/setup-admin.js", "admin:check": "node scripts/check-supabase-connection.js", "supabase:setup": "npm run supabase:fix-key", "supabase:fix-key": "node scripts/fix-supabase-key.js", "supabase:update-key": "node scripts/update-env-supabase-key.js", "supabase:verify-key": "node scripts/verify-fix-supabase-key.js", "supabase:get-key": "node scripts/get-supabase-service-key.js", "check:translations": "tsc && node scripts/check-translations.js", "test:email": "tsc && node scripts/test-reimbursement-email.js", "test:email:direct": "node scripts/test-email-direct.js", "setup:drive": "node scripts/setup-google-drive.js", "setup:drive:api": "node scripts/setup-drive-api.js", "cleanup:temp": "node scripts/cleanup-temp-files.js"}, "dependencies": {"@emotion/react": "^11.14.0", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tabs": "^1.1.12", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@sendgrid/mail": "^8.1.5", "@supabase/supabase-js": "^2.49.4", "@types/cheerio": "^0.22.35", "@types/pg": "^8.12.0", "@types/react-calendar": "^3.9.0", "@types/uuid": "^10.0.0", "bcryptjs": "^3.0.2", "cheerio": "^1.0.0", "class-variance-authority": "^0.7.1", "critters": "^0.0.23", "cross-env": "^7.0.3", "dotenv": "^16.4.7", "express": "^4.18.3", "file-saver": "^2.0.5", "form-data": "^4.0.0", "formidable": "^3.5.2", "framer-motion": "^12.6.3", "googleapis": "^133.0.0", "http-proxy-middleware": "^2.0.6", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "jszip": "^3.10.1", "lucide-react": "^0.363.0", "next": "14.2.3", "node-fetch": "^2.7.0", "nodemailer": "^6.10.0", "papaparse": "^5.5.2", "pdf-lib": "^1.17.1", "pdf-parse": "^1.1.1", "pdfjs-dist": "^3.4.120", "pg": "^8.15.6", "playwright": "^1.52.0", "qrcode": "^1.5.4", "react": "^18.2.0", "react-calendar": "^5.1.0", "react-confetti": "^6.4.0", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.55.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-pdf": "^9.2.1", "react-toastify": "^11.0.5", "speakeasy": "^2.0.0", "tailwind-merge": "^3.3.0", "task-master-ai": "^0.13.2", "twilio": "^5.5.1", "uuid": "^11.1.0", "xlsx": "^0.18.5", "zod": "^3.24.2"}, "devDependencies": {"@babel/plugin-transform-runtime": "^7.26.10", "@tailwindcss/typography": "^0.5.10", "@types/bcryptjs": "^2.4.6", "@types/formidable": "^3.4.5", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "autoprefixer": "^10.0.1", "buffer": "^6.0.3", "eslint": "^8", "eslint-config-next": "14.2.3", "postcss": "^8", "rimraf": "^6.0.1", "stream-browserify": "^3.0.0", "tailwindcss": "^3.3.0", "ts-node": "^10.9.2", "typescript": "^5", "typescript-eslint": "^8.35.0", "util": "^0.12.5"}}
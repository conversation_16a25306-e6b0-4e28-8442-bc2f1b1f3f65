#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pdfplumber
import PyPDF2
import sys
import os

def analyze_pdf_with_pdfplumber(file_path):
    """Analisa PDF usando pdfplumber"""
    try:
        with pdfplumber.open(file_path) as pdf:
            print(f"=== ANÁLISE COM PDFPLUMBER ===")
            print(f"Número de páginas: {len(pdf.pages)}")
            print(f"Metadados: {pdf.metadata}")
            
            for i, page in enumerate(pdf.pages):
                print(f"\n--- PÁGINA {i+1} ---")
                print(f"Dimensões: {page.width} x {page.height}")
                
                # Extrair texto
                text = page.extract_text()
                if text:
                    print("TEXTO EXTRAÍDO:")
                    print(text[:1500])  # Primeiros 1500 caracteres
                    if len(text) > 1500:
                        print("... (texto truncado)")
                else:
                    print("Não foi possível extrair texto desta página")
                
                # Tentar extrair tabelas
                tables = page.extract_tables()
                if tables:
                    print(f"\nTABELAS ENCONTRADAS: {len(tables)}")
                    for j, table in enumerate(tables):
                        print(f"Tabela {j+1}:")
                        for row in table[:5]:  # Primeiras 5 linhas
                            print(row)
                        if len(table) > 5:
                            print("... (mais linhas)")
                
                print("-" * 50)
                
    except Exception as e:
        print(f"Erro com pdfplumber: {e}")
        return False
    return True

def analyze_pdf_with_pypdf2(file_path):
    """Analisa PDF usando PyPDF2"""
    try:
        with open(file_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            print(f"\n=== ANÁLISE COM PYPDF2 ===")
            print(f"Número de páginas: {len(pdf_reader.pages)}")
            
            if pdf_reader.metadata:
                print(f"Metadados: {pdf_reader.metadata}")
            
            for i, page in enumerate(pdf_reader.pages):
                print(f"\n--- PÁGINA {i+1} ---")
                text = page.extract_text()
                if text:
                    print("TEXTO EXTRAÍDO:")
                    print(text[:1500])
                    if len(text) > 1500:
                        print("... (texto truncado)")
                else:
                    print("Não foi possível extrair texto desta página")
                print("-" * 50)
                
    except Exception as e:
        print(f"Erro com PyPDF2: {e}")
        return False
    return True

def main():
    file_path = "DADOS/Extrato Mensal - 06.2025 ABZ FMS.pdf"
    
    if not os.path.exists(file_path):
        print(f"Arquivo não encontrado: {file_path}")
        return
    
    print(f"Analisando arquivo: {file_path}")
    print(f"Tamanho do arquivo: {os.path.getsize(file_path)} bytes")
    
    # Tentar com pdfplumber primeiro
    success = analyze_pdf_with_pdfplumber(file_path)
    
    # Se falhar, tentar com PyPDF2
    if not success:
        analyze_pdf_with_pypdf2(file_path)

if __name__ == "__main__":
    main()
